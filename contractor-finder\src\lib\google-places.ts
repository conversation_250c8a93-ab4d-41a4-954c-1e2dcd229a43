import { Client } from '@googlemaps/google-maps-services-js'

const client = new Client({})

export interface PlaceResult {
  place_id: string
  name: string
  formatted_address: string
  formatted_phone_number?: string
  website?: string
  rating?: number
  user_ratings_total?: number
  business_status?: string
  types: string[]
}

export interface ContractorSearchParams {
  query: string
  location: string
  radius?: number
  type?: string
}

export class GooglePlacesService {
  private apiKey: string

  constructor() {
    this.apiKey = process.env.GOOGLE_PLACES_API_KEY!
    if (!this.apiKey) {
      throw new Error('Google Places API key is required')
    }
  }

  async searchContractors(params: ContractorSearchParams): Promise<PlaceResult[]> {
    try {
      const searchQuery = `${params.query} ${params.location} New Jersey`
      
      const response = await client.textSearch({
        params: {
          query: searchQuery,
          key: this.apiKey,
          region: 'us',
          type: 'establishment'
        }
      })

      const places = response.data.results || []
      
      // Filter for contractor-related businesses
      const contractorTypes = [
        'electrician', 'plumber', 'contractor', 'roofer', 'painter',
        'hvac', 'flooring', 'landscaping', 'handyman', 'carpenter'
      ]

      const filteredPlaces = places.filter(place => {
        const types = place.types || []
        const name = place.name?.toLowerCase() || ''
        
        return contractorTypes.some(type => 
          types.includes(type) || 
          name.includes(type) ||
          name.includes('construction') ||
          name.includes('repair') ||
          name.includes('service')
        )
      })

      return filteredPlaces.map(place => ({
        place_id: place.place_id!,
        name: place.name!,
        formatted_address: place.formatted_address!,
        formatted_phone_number: place.formatted_phone_number,
        website: place.website,
        rating: place.rating,
        user_ratings_total: place.user_ratings_total,
        business_status: place.business_status,
        types: place.types || []
      }))
    } catch (error) {
      console.error('Error searching contractors:', error)
      throw new Error('Failed to search contractors')
    }
  }

  async getPlaceDetails(placeId: string): Promise<PlaceResult | null> {
    try {
      const response = await client.placeDetails({
        params: {
          place_id: placeId,
          key: this.apiKey,
          fields: [
            'place_id', 'name', 'formatted_address', 'formatted_phone_number',
            'website', 'rating', 'user_ratings_total', 'business_status', 'types'
          ]
        }
      })

      const place = response.data.result
      if (!place) return null

      return {
        place_id: place.place_id!,
        name: place.name!,
        formatted_address: place.formatted_address!,
        formatted_phone_number: place.formatted_phone_number,
        website: place.website,
        rating: place.rating,
        user_ratings_total: place.user_ratings_total,
        business_status: place.business_status,
        types: place.types || []
      }
    } catch (error) {
      console.error('Error getting place details:', error)
      return null
    }
  }

  // Filter contractors without websites
  filterContractorsWithoutWebsites(places: PlaceResult[]): PlaceResult[] {
    return places.filter(place => !place.website || place.website.trim() === '')
  }
}
