'use client'

import { useState } from 'react'
import { Search, MapPin, Wrench } from 'lucide-react'

interface SearchFormProps {
  onSearch: (query: string, location: string, tradeType: string) => void
  loading: boolean
}

const TRADE_TYPES = [
  'All Trades',
  'Plumber',
  'Electrician',
  'HVAC Contractor',
  '<PERSON>oof<PERSON>',
  '<PERSON>',
  'Flooring Contractor',
  'Landscaper',
  '<PERSON><PERSON>',
  '<PERSON>',
  'General Contractor',
  'Concrete Contractor',
  '<PERSON>ce Contractor',
  'Pool Contractor',
  'Solar Installer'
]

const NJ_CITIES = [
  'Newark',
  'Jersey City',
  'Paterson',
  'Elizabeth',
  'Edison',
  'Woodbridge',
  'Lakewood',
  'Toms River',
  'Hamilton',
  'Trenton',
  'Clifton',
  'Camden',
  'Brick',
  'Cherry Hill',
  'Passaic',
  'Union City',
  'Middletown',
  'Gloucester',
  'East Orange',
  'Bayonne'
]

export default function SearchForm({ onSearch, loading }: SearchFormProps) {
  const [tradeType, setTradeType] = useState('All Trades')
  const [location, setLocation] = useState('')
  const [customQuery, setCustomQuery] = useState('')

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!location.trim()) {
      alert('Please select or enter a location')
      return
    }

    const query = customQuery.trim() || tradeType
    onSearch(query, location, tradeType)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Trade Type */}
        <div>
          <label htmlFor="trade-type" className="block text-sm font-medium text-gray-700 mb-2">
            <Wrench className="h-4 w-4 inline mr-1" />
            Trade Type
          </label>
          <select
            id="trade-type"
            value={tradeType}
            onChange={(e) => setTradeType(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {TRADE_TYPES.map((trade) => (
              <option key={trade} value={trade}>
                {trade}
              </option>
            ))}
          </select>
        </div>

        {/* Location */}
        <div>
          <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
            <MapPin className="h-4 w-4 inline mr-1" />
            Location (NJ)
          </label>
          <input
            type="text"
            id="location"
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            placeholder="Enter city name..."
            list="nj-cities"
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <datalist id="nj-cities">
            {NJ_CITIES.map((city) => (
              <option key={city} value={city} />
            ))}
          </datalist>
        </div>

        {/* Custom Search Query */}
        <div>
          <label htmlFor="custom-query" className="block text-sm font-medium text-gray-700 mb-2">
            <Search className="h-4 w-4 inline mr-1" />
            Custom Search (Optional)
          </label>
          <input
            type="text"
            id="custom-query"
            value={customQuery}
            onChange={(e) => setCustomQuery(e.target.value)}
            placeholder="e.g., kitchen remodeling"
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Search Button */}
      <div className="flex justify-center">
        <button
          type="submit"
          disabled={loading}
          className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <>
              <div className="animate-spin -ml-1 mr-3 h-5 w-5 border-2 border-white border-t-transparent rounded-full"></div>
              Searching...
            </>
          ) : (
            <>
              <Search className="h-5 w-5 mr-2" />
              Find Contractors
            </>
          )}
        </button>
      </div>

      {/* Search Tips */}
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
        <h4 className="text-sm font-medium text-blue-800 mb-2">Search Tips:</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Results will only show contractors without websites</li>
          <li>• Try different trade types to find more opportunities</li>
          <li>• Use custom search for specific services (e.g., "bathroom renovation")</li>
          <li>• Search multiple cities to expand your lead pool</li>
        </ul>
      </div>
    </form>
  )
}
