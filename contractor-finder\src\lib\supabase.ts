import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabase<PERSON>nonKey)

// Types for our database tables
export interface Contractor {
  id: string
  name: string
  business_name?: string
  phone?: string
  email?: string
  address?: string
  city: string
  state: string
  zip_code?: string
  trade_type: string
  rating?: number
  review_count?: number
  has_website: boolean
  website_url?: string
  google_place_id?: string
  notes?: string
  status: 'new' | 'contacted' | 'interested' | 'not_interested' | 'converted'
  created_at: string
  updated_at: string
}

export interface Search {
  id: string
  query: string
  location: string
  trade_type: string
  results_count: number
  created_at: string
}

export interface User {
  id: string
  email: string
  full_name?: string
  created_at: string
}
