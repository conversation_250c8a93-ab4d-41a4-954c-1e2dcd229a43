'use client'

import { useState } from 'react'
import { Search, Building2, Phone, Mail, MapPin, Star, ExternalLink, Download, Filter } from 'lucide-react'
import SearchForm from './SearchForm'
import ContractorTable from './ContractorTable'
import { Contractor } from '@/lib/supabase'

export default function Dashboard() {
  const [contractors, setContractors] = useState<Contractor[]>([])
  const [loading, setLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  const handleSearch = async (query: string, location: string, tradeType: string) => {
    setLoading(true)
    setSearchQuery(`${tradeType} in ${location}`)
    
    try {
      // TODO: Implement actual search functionality
      // For now, we'll show mock data
      const mockContractors: Contractor[] = [
        {
          id: '1',
          name: '<PERSON>',
          business_name: 'Smith Plumbing Services',
          phone: '(*************',
          email: '<EMAIL>',
          address: '123 Main St',
          city: 'Newark',
          state: 'NJ',
          zip_code: '07102',
          trade_type: 'Plumber',
          rating: 4.5,
          review_count: 23,
          has_website: false,
          status: 'new',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: '2',
          name: 'Mike Johnson',
          business_name: 'Johnson Electric',
          phone: '(*************',
          address: '456 Oak Ave',
          city: 'Jersey City',
          state: 'NJ',
          zip_code: '07302',
          trade_type: 'Electrician',
          rating: 4.2,
          review_count: 18,
          has_website: false,
          status: 'new',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ]
      
      setContractors(mockContractors)
    } catch (error) {
      console.error('Search failed:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleExport = () => {
    // TODO: Implement CSV export
    console.log('Exporting contractors...')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Building2 className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">ContractorFinder</h1>
                <p className="text-sm text-gray-500">Find NJ contractors without websites</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={handleExport}
                disabled={contractors.length === 0}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Download className="h-4 w-4 mr-2" />
                Export CSV
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Section */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
          <div className="flex items-center mb-4">
            <Search className="h-5 w-5 text-gray-400 mr-2" />
            <h2 className="text-lg font-medium text-gray-900">Search Contractors</h2>
          </div>
          <SearchForm onSearch={handleSearch} loading={loading} />
        </div>

        {/* Results Section */}
        {searchQuery && (
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Search Results</h3>
                  <p className="text-sm text-gray-500">
                    {loading ? 'Searching...' : `Found ${contractors.length} contractors for "${searchQuery}"`}
                  </p>
                </div>
                {contractors.length > 0 && (
                  <div className="flex items-center space-x-2">
                    <Filter className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-500">No website only</span>
                  </div>
                )}
              </div>
            </div>
            <ContractorTable contractors={contractors} loading={loading} />
          </div>
        )}

        {/* Empty State */}
        {!searchQuery && (
          <div className="text-center py-12">
            <Building2 className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No searches yet</h3>
            <p className="mt-1 text-sm text-gray-500">
              Start by searching for contractors in New Jersey
            </p>
          </div>
        )}
      </main>
    </div>
  )
}
