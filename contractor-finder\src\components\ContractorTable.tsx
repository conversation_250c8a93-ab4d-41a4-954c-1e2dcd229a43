'use client'

import { useState } from 'react'
import { Phone, Mail, MapPin, Star, Building2, User, MessageSquare, CheckCircle, XCircle, Clock } from 'lucide-react'
import { Contractor } from '@/lib/supabase'
import { formatPhoneNumber } from '@/lib/utils'

interface ContractorTableProps {
  contractors: Contractor[]
  loading: boolean
}

const STATUS_COLORS = {
  new: 'bg-blue-100 text-blue-800',
  contacted: 'bg-yellow-100 text-yellow-800',
  interested: 'bg-green-100 text-green-800',
  not_interested: 'bg-red-100 text-red-800',
  converted: 'bg-purple-100 text-purple-800'
}

const STATUS_ICONS = {
  new: Clock,
  contacted: MessageSquare,
  interested: CheckCircle,
  not_interested: XCircle,
  converted: CheckCircle
}

export default function ContractorTable({ contractors, loading }: ContractorTableProps) {
  const [selectedContractor, setSelectedContractor] = useState<Contractor | null>(null)
  const [notes, setNotes] = useState('')

  const handleStatusChange = (contractorId: string, newStatus: Contractor['status']) => {
    // TODO: Implement status update in database
    console.log(`Updating contractor ${contractorId} status to ${newStatus}`)
  }

  const handleAddNote = (contractorId: string) => {
    if (!notes.trim()) return
    
    // TODO: Implement note saving to database
    console.log(`Adding note to contractor ${contractorId}: ${notes}`)
    setNotes('')
    setSelectedContractor(null)
  }

  if (loading) {
    return (
      <div className="p-8">
        <div className="animate-pulse space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-16 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    )
  }

  if (contractors.length === 0) {
    return (
      <div className="p-8 text-center">
        <Building2 className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <p className="text-gray-500">No contractors found. Try adjusting your search criteria.</p>
      </div>
    )
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Contractor
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Contact Info
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Location
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Rating
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {contractors.map((contractor) => {
            const StatusIcon = STATUS_ICONS[contractor.status]
            
            return (
              <tr key={contractor.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                        <User className="h-5 w-5 text-blue-600" />
                      </div>
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">
                        {contractor.name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {contractor.business_name}
                      </div>
                      <div className="text-xs text-blue-600 font-medium">
                        {contractor.trade_type}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="space-y-1">
                    {contractor.phone && (
                      <div className="flex items-center text-sm text-gray-900">
                        <Phone className="h-4 w-4 text-gray-400 mr-2" />
                        <a 
                          href={`tel:${contractor.phone}`}
                          className="hover:text-blue-600"
                        >
                          {formatPhoneNumber(contractor.phone)}
                        </a>
                      </div>
                    )}
                    {contractor.email && (
                      <div className="flex items-center text-sm text-gray-900">
                        <Mail className="h-4 w-4 text-gray-400 mr-2" />
                        <a 
                          href={`mailto:${contractor.email}`}
                          className="hover:text-blue-600"
                        >
                          {contractor.email}
                        </a>
                      </div>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center text-sm text-gray-900">
                    <MapPin className="h-4 w-4 text-gray-400 mr-2" />
                    <div>
                      <div>{contractor.city}, {contractor.state}</div>
                      {contractor.zip_code && (
                        <div className="text-xs text-gray-500">{contractor.zip_code}</div>
                      )}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {contractor.rating && (
                    <div className="flex items-center">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="ml-1 text-sm text-gray-900">
                        {contractor.rating}
                      </span>
                      {contractor.review_count && (
                        <span className="ml-1 text-xs text-gray-500">
                          ({contractor.review_count})
                        </span>
                      )}
                    </div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <select
                    value={contractor.status}
                    onChange={(e) => handleStatusChange(contractor.id, e.target.value as Contractor['status'])}
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border-0 ${STATUS_COLORS[contractor.status]}`}
                  >
                    <option value="new">New</option>
                    <option value="contacted">Contacted</option>
                    <option value="interested">Interested</option>
                    <option value="not_interested">Not Interested</option>
                    <option value="converted">Converted</option>
                  </select>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    onClick={() => setSelectedContractor(contractor)}
                    className="text-blue-600 hover:text-blue-900 mr-4"
                  >
                    Add Note
                  </button>
                  {contractor.phone && (
                    <a
                      href={`tel:${contractor.phone}`}
                      className="text-green-600 hover:text-green-900"
                    >
                      Call
                    </a>
                  )}
                </td>
              </tr>
            )
          })}
        </tbody>
      </table>

      {/* Add Note Modal */}
      {selectedContractor && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Add Note for {selectedContractor.name}
              </h3>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Enter your notes..."
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <div className="flex justify-end space-x-3 mt-4">
                <button
                  onClick={() => setSelectedContractor(null)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleAddNote(selectedContractor.id)}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md"
                >
                  Save Note
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
